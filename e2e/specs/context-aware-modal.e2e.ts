/**
 * E2E tests for context-aware modal functionality
 */

import { test, expect, beforeEach, afterEach, describe } from 'vitest';
import {
  setupObsidianWithTaskSync,
  createTestFolders,
  isElementVisible,
  isElementEnabled,
  elementHasClass,
  waitForElementVisible
} from '../helpers/task-sync-setup';
import type { Page, ElectronApplication } from 'playwright';

describe('Context-Aware Task Modal', () => {
  let electronApp: ElectronApplication;
  let page: Page;

  beforeEach(async () => {
    const setup = await setupObsidianWithTaskSync('./e2e/test_obsidian_data/vault', './e2e/test_obsidian_data');
    electronApp = setup.electronApp;
    page = setup.page;

    await createTestFolders(page);
  });

  afterEach(async () => {
    if (electronApp) {
      await electronApp.close();
    }
  });

  test('should open basic modal when no context', async () => {
    // Open modal via command palette
    await page.keyboard.press('Control+p');
    await page.fill('.prompt-input', 'Add Task');
    await page.keyboard.press('Enter');

    // Wait for modal to appear
    await waitForElementVisible(page, '.task-sync-create-task');

    // Check modal title
    const title = await page.textContent('.modal-title');
    expect(title).toBe('Create New Task');

    // Check that context info is not shown
    const contextInfoVisible = await isElementVisible(page, '.task-sync-context-info');
    expect(contextInfoVisible).toBe(false);

    // Check that project and area fields are enabled
    const projectFieldEnabled = await isElementEnabled(page, 'input[placeholder*="Project"]');
    const areaFieldEnabled = await isElementEnabled(page, 'input[placeholder*="Area"]');

    expect(projectFieldEnabled).toBe(true);
    expect(areaFieldEnabled).toBe(true);
  });

  test('should show project context when opened from project file', async () => {
    // Create a project file first
    await page.evaluate(async () => {
      const app = (window as any).app;
      await app.vault.create('Projects/Test Project.md', `# Test Project

This is a test project file.

## Objectives
- Complete the project
- Test functionality
`);
    });

    // Open the project file
    await page.evaluate(async () => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath('Projects/Test Project.md');
      if (file) {
        await app.workspace.getLeaf().openFile(file);
      }
    });

    // Open modal via command palette
    await page.keyboard.press('Control+p');
    await page.fill('.prompt-input', 'Add Task');
    await page.keyboard.press('Enter');

    // Wait for modal to appear
    await waitForElementVisible(page, '.task-sync-create-task');

    // Check modal title includes project context
    const title = await page.textContent('.modal-title');
    expect(title).toContain('Create Task for Project: Test Project');

    // Check that context info is shown
    const contextInfoVisible = await isElementVisible(page, '.task-sync-context-info');
    expect(contextInfoVisible).toBe(true);
  });

  test('should use Obsidian Setting components for form fields', async () => {
    // Open modal
    await page.keyboard.press('Control+p');
    await page.fill('.prompt-input', 'Add Task');
    await page.keyboard.press('Enter');

    // Wait for modal to appear
    await waitForElementVisible(page, '.task-sync-create-task');

    // Check that Setting components are used (they have specific CSS classes)
    const settingItems = page.locator('.setting-item');
    const settingCount = await settingItems.count();

    // Should have multiple setting items for different fields
    expect(settingCount).toBeGreaterThan(5);

    // Check specific setting items exist
    const taskNameVisible = await isElementVisible(page, '.setting-item:has-text("Task Name")');
    const typeVisible = await isElementVisible(page, '.setting-item:has-text("Type")');
    const projectVisible = await isElementVisible(page, '.setting-item:has-text("Project")');

    expect(taskNameVisible).toBe(true);
    expect(typeVisible).toBe(true);
    expect(projectVisible).toBe(true);
  });

  test('should have improved styling with Obsidian components', async () => {
    // Open modal
    await page.keyboard.press('Control+p');
    await page.fill('.prompt-input', 'Add Task');
    await page.keyboard.press('Enter');

    // Wait for modal to appear
    await waitForElementVisible(page, '.task-sync-create-task');

    // Check that modal has proper CSS classes
    const modalHasClass = await elementHasClass(page, '.task-sync-create-task', /task-sync-modal/);
    expect(modalHasClass).toBe(true);

    // Check that form actions have proper styling
    const cancelButtonVisible = await isElementVisible(page, 'button.mod-cancel');
    const submitButtonVisible = await isElementVisible(page, 'button.mod-cta');

    expect(cancelButtonVisible).toBe(true);
    expect(submitButtonVisible).toBe(true);

    // Check button text
    const cancelText = await page.textContent('button.mod-cancel');
    const submitText = await page.textContent('button.mod-cta');

    expect(cancelText).toBe('Cancel');
    expect(submitText).toBe('Create Task');
  });
});
