/**
 * E2E tests for context-aware modal functionality
 */

import { test, expect } from '@playwright/test';
import {
  setupObsidianWithTaskSync,
  createTestFolders,
  getFileContent,
  fileExists
} from '../helpers/task-sync-setup';
import type { Page, ElectronApplication } from 'playwright';

test.describe('Context-Aware Task Modal', () => {
  let electronApp: ElectronApplication;
  let page: Page;

  test.beforeEach(async () => {
    const setup = await setupObsidianWithTaskSync('./e2e/test_obsidian_data/vault', './e2e/test_obsidian_data');
    electronApp = setup.electronApp;
    page = setup.page;

    await createTestFolders(page);
  });

  test.afterEach(async () => {
    if (electronApp) {
      await electronApp.close();
    }
  });

  test('should open basic modal when no context', async () => {
    // Open modal via command palette
    await page.keyboard.press('Control+p');
    await page.fill('.prompt-input', 'Add Task');
    await page.keyboard.press('Enter');

    // Wait for modal to appear
    await page.waitForSelector('.task-sync-create-task', { timeout: 5000 });

    // Check modal title
    const title = await page.textContent('.modal-title');
    expect(title).toBe('Create New Task');

    // Check that context info is not shown
    const contextInfo = page.locator('.task-sync-context-info');
    await expect(contextInfo).not.toBeVisible();

    // Check that project and area fields are enabled
    const projectField = page.locator('input[placeholder*="Project"]');
    const areaField = page.locator('input[placeholder*="Area"]');

    await expect(projectField).toBeEnabled();
    await expect(areaField).toBeEnabled();
  });

  test('should show project context when opened from project file', async () => {
    // Create a project file first
    await page.evaluate(async () => {
      const app = (window as any).app;
      await app.vault.create('Projects/Test Project.md', `# Test Project

This is a test project file.

## Objectives
- Complete the project
- Test functionality
`);
    });

    // Open the project file
    await page.evaluate(async () => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath('Projects/Test Project.md');
      if (file) {
        await app.workspace.getLeaf().openFile(file);
      }
    });

    // Open modal via command palette
    await page.keyboard.press('Control+p');
    await page.fill('.prompt-input', 'Add Task');
    await page.keyboard.press('Enter');

    // Wait for modal to appear
    await page.waitForSelector('.task-sync-create-task', { timeout: 5000 });

    // Check modal title includes project context
    const title = await page.textContent('.modal-title');
    expect(title).toContain('Create Task for Project: Test Project');

    // Check that context info is shown
    const contextInfo = page.locator('.task-sync-context-info');
    await expect(contextInfo).toBeVisible();
  });

  test('should use Obsidian Setting components for form fields', async () => {
    // Open modal
    await page.keyboard.press('Control+p');
    await page.fill('.prompt-input', 'Add Task');
    await page.keyboard.press('Enter');

    // Wait for modal to appear
    await page.waitForSelector('.task-sync-create-task', { timeout: 5000 });

    // Check that Setting components are used (they have specific CSS classes)
    const settingItems = page.locator('.setting-item');
    const settingCount = await settingItems.count();

    // Should have multiple setting items for different fields
    expect(settingCount).toBeGreaterThan(5);

    // Check specific setting items exist
    await expect(page.locator('.setting-item').filter({ hasText: 'Task Name' })).toBeVisible();
    await expect(page.locator('.setting-item').filter({ hasText: 'Type' })).toBeVisible();
    await expect(page.locator('.setting-item').filter({ hasText: 'Project' })).toBeVisible();
  });

  test('should have improved styling with Obsidian components', async () => {
    // Open modal
    await page.keyboard.press('Control+p');
    await page.fill('.prompt-input', 'Add Task');
    await page.keyboard.press('Enter');

    // Wait for modal to appear
    await page.waitForSelector('.task-sync-create-task', { timeout: 5000 });

    // Check that modal has proper CSS classes
    const modal = page.locator('.task-sync-create-task');
    await expect(modal).toHaveClass(/task-sync-modal/);

    // Check that form actions have proper styling
    const cancelButton = page.locator('button.mod-cancel');
    const submitButton = page.locator('button.mod-cta');

    await expect(cancelButton).toBeVisible();
    await expect(submitButton).toBeVisible();

    // Check button text
    expect(await cancelButton.textContent()).toBe('Cancel');
    expect(await submitButton.textContent()).toBe('Create Task');
  });
});
